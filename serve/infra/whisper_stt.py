from __future__ import annotations

import asyncio
import dataclasses
import logging
from typing import List, Optional, Set, <PERSON>ple

import aiohttp
from livekit import rtc
from livekit.agents import stt, APIConnectOptions, APITimeoutError, APIStatusError, APIConnectionError, \
    DEFAULT_API_CONNECT_OPTIONS
from livekit.agents.stt import SpeechStream
from livekit.agents.utils import AudioBuffer

from serve.infra.whisper_stream_soc import WhisperStream

logger = logging.getLogger(__name__)


@dataclasses.dataclass
class WisperOptions:
    language: Optional[str] = None
    model: str = "base"
    interim_results: bool = True
    punctuate: bool = True
    smart_format: bool = True
    sample_rate: int = 16000
    no_delay: bool = False
    endpointing_ms: int = 1000
    keywords: List[Tuple[str, float]] = dataclasses.field(default_factory=list)
    keyterms: List[str] = dataclasses.field(default_factory=list)
    detect_language: bool = False
    profanity_filter: bool = False


class WhiSTT(stt.STT):
    def __init__(
            self,
            *,
            # base_url: str = "wss://whisper.gpu.dev.callevate.ai:443",
            base_url: str = "ws://localhost:8000/asr",
            language: str | None = None,
            model: str = "base",
            interim_results: bool = False,
            punctuate: bool = True,
            smart_format: bool = True,
            sample_rate: int = 16000,
            no_delay: bool = False,
            endpointing_ms: int = 1000,
            keywords: List[Tuple[str, float]] | None = None,
            keyterms: List[str] | None = None,
            profanity_filter: bool = False,
    ):
        super().__init__(capabilities=stt.STTCapabilities(streaming=True, interim_results=interim_results))
        self._api_key = ""
        self._base_url = base_url
        self._session: Optional[aiohttp.ClientSession] = None
        self._streams: Set[SpeechStream] = set()

        self._opts = WisperOptions(
            language=language,
            model=model,
            interim_results=interim_results,
            punctuate=punctuate,
            smart_format=smart_format,
            sample_rate=sample_rate,
            no_delay=no_delay,
            endpointing_ms=endpointing_ms,
            keywords=keywords or [],
            keyterms=keyterms or [],
            profanity_filter=profanity_filter,
        )

    def _ensure_session(self) -> aiohttp.ClientSession:
        if self._session is None or self._session.closed:
            self._session = aiohttp.ClientSession()
        return self._session

    async def _recognize_impl(
            self,
            buffer: AudioBuffer,
            *,
            language: str | None,
            conn_options: APIConnectOptions,
    ) -> stt.SpeechEvent:
        config = self._sanitize_options(language=language)

        recognize_config = {
            "model": str(config.model),
            "punctuate": config.punctuate,
            "detect_language": config.detect_language,
            "keywords": self._opts.keywords,
        }
        if config.language:
            recognize_config["language"] = config.language

        try:
            async with self._ensure_session().post(
                    url=f"{self._base_url}/asr",
                    data=rtc.combine_audio_frames(buffer).to_wav_bytes(),
                    headers={
                        "Authorization": f"Bearer {self._api_key}",
                        "Content-Type": "audio/wav",
                    },
                    timeout=aiohttp.ClientTimeout(total=30, sock_connect=conn_options.timeout),
            ) as res:
                data = await res.json()
                return prerecorded_transcription_to_speech_event(
                    config.language,
                    data,
                )

        except asyncio.TimeoutError as e:
            raise APITimeoutError() from e
        except aiohttp.ClientResponseError as e:
            raise APIStatusError(
                message=e.message,
                status_code=e.status,
                request_id=None,
                body=None,
            ) from e
        except Exception as e:
            raise APIConnectionError() from e

    def stream(
            self,
            *,
            language: str | None = None,
            conn_options: APIConnectOptions = DEFAULT_API_CONNECT_OPTIONS,
    ) -> SpeechStream:

        config = self._sanitize_options(language=language)
        # stream = SpeechStream(
        stream = WhisperStream(

            stt=self,
            opts=config,
            conn_options=conn_options,

        )
        self._streams.add(stream)
        return stream

    async def aclose(self):
        """Close the ClientSession when it's no longer needed."""
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None

    def _sanitize_options(self, *, language: str | None = None) -> WisperOptions:
        config = dataclasses.replace(self._opts)
        config.language = language or config.language

        if config.detect_language:
            config.language = None

        return config


def prerecorded_transcription_to_speech_event(
        language: str | None,
        data: dict,
) -> stt.SpeechEvent:
    return stt.SpeechEvent(
        type=stt.SpeechEventType.FINAL_TRANSCRIPT,
        alternatives=[
            stt.SpeechData(
                language=language,
                start_time=data.get("start_time", 0),
                end_time=data.get("end_time", 0),
                confidence=data.get("confidence", 1.0),
                text=data.get("text", ""),
            )
        ],
    )
