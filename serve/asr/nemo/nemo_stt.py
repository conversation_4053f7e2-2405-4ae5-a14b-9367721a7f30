import io
import wave
from dataclasses import dataclass
from enum import Enum
from typing import Any, Optional

import numpy as np
import torch
import torchaudio
from livekit.agents import stt
from livekit.rtc.audio_frame import AudioFrame

from .log import logger
from .model_server import NemoModelServer  # Updated import


class NemoModel(str, Enum):
    CONFORMER_CTC = "stt_en_conformer_ctc_large"
    CONFORMER_TRANSDUCER = "stt_en_conformer_transducer_large"
    QUARTZNET_ARABIC = "livekit-plugins-nemo/models/quartznet_15x5_ara.nemo"
    PARA_MULTI = "Parakeet-CTC-XXL-1.1B"


@dataclass
class NemoSTTOptions:
    model: NemoModel = NemoModel.CONFORMER_CTC
    device: str = "auto"
    sample_rate: int = 16000
    num_channels: int = 1
    language: str = "eng"


class NemoSTT(stt.STT):
    def __init__(self, *, opts: NemoSTTOptions | None = None):
        super().__init__(
            capabilities=stt.STTCapabilities(streaming=True, interim_results=True)
        )
        self._opts = opts or NemoSTTOptions()
        if self._opts.language == "ara":
            self._opts.model = NemoModel.QUARTZNET_ARABIC
        self._model_server: Optional[NemoModelServer] = None
        self._content, self._wave = self._new_wave()

    async def _load(self):
        logger.debug(f"Loading NeMo model for {self._opts.language}...")
        self._model_server = NemoModelServer(
            model_name=self._opts.model.value,
            device=self._opts.device
        )
        await self._model_server.load_model()
        logger.debug(f"Loaded NeMo model: {self._opts.model.value}")

    @classmethod
    async def load(cls, opts: NemoSTTOptions | None = None) -> "NemoSTT":
        instance = cls(opts=opts)
        #   await instance._load()
        return instance

    def _new_wave(self):
        content = io.BytesIO()
        ww = wave.open(content, "wb")
        ww.setsampwidth(2)
        ww.setnchannels(self._opts.num_channels)
        ww.setframerate(self._opts.sample_rate)
        return (content, ww)

    async def _recognize_impl(
            self,
            frame: AudioFrame,
            *,
            language: str | None = None,
            conn_options: Any = None,
    ) -> stt.SpeechEvent:
        if not self._model_server:
            logger.error("NeMo model not available")
            return stt.SpeechEvent(
                type=stt.SpeechEventType.FINAL_TRANSCRIPT,
                alternatives=[
                    stt.SpeechData(text="", language=self._opts.language, confidence=0.0)
                ],
            )

        try:
            # Convert AudioFrame to numpy array and normalize
            audio_data = frame.data.tobytes()
            audio_float = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0

            # Convert numpy array to torch tensor
            audio_tensor = torch.from_numpy(audio_float).unsqueeze(0)

            # Resample if needed
            if frame.sample_rate != self._opts.sample_rate:
                resampler = torchaudio.transforms.Resample(
                    orig_freq=frame.sample_rate,
                    new_freq=self._opts.sample_rate
                )
                audio_tensor = resampler(audio_tensor)

            # Remove extra channel dimension if present
            if audio_tensor.ndim == 3 and audio_tensor.shape[1] == 1:
                audio_tensor = audio_tensor.squeeze(1)

            # Move tensor to model device
            audio_tensor = audio_tensor.to(self._model_server.device)

            # Transcribe using model server
            transcription = await self._model_server.transcribe(audio_tensor)

            return stt.SpeechEvent(
                type=stt.SpeechEventType.FINAL_TRANSCRIPT,
                alternatives=[
                    stt.SpeechData(
                        text=transcription,
                        language=self._opts.language,
                        confidence=1.0,
                    )
                ],
            )
        except Exception as e:
            logger.error(f"NeMo transcription error: {e}")
            return stt.SpeechEvent(
                type=stt.SpeechEventType.FINAL_TRANSCRIPT,
                alternatives=[
                    stt.SpeechData(text="", language=self._opts.language, confidence=0.0)
                ],
            )
