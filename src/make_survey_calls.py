import asyncio
import csv
import json
import logging
import os
import time
from pathlib import Path
from dotenv import load_dotenv
from livekit import api
from datetime import datetime

load_dotenv(dotenv_path=Path(__file__).parent.parent.parent / '.env')

logger = logging.getLogger("make-survey-calls")
logger.setLevel(logging.INFO)

# Configuration
room_name_prefix = "survey-call-"
agent_name = "survey-agent"
outbound_trunk_id = os.getenv("SIP_OUTBOUND_TRUNK_ID")
csv_file_path = Path(__file__).parent / "survey_data.csv"

async def make_survey_call(phone_number, question, row_index):
    """Create a dispatch and add a SIP participant to call the phone number with survey question"""
    # Create a unique room name for each call using the prefix and row index
    room_name = f"{room_name_prefix}{row_index}"

    # Create metadata as JSON containing all relevant data
    metadata = json.dumps({
        "phone_number": phone_number,
        "question": question,
        "row_index": row_index
    })

    lkapi = api.LiveKitAPI()

    logger.info(f"Creating dispatch for agent {agent_name} in room {room_name}")

    dispatch = await lkapi.agent_dispatch.create_dispatch(
        api.CreateAgentDispatchRequest(
            agent_name=agent_name,
            room=room_name,
            metadata=metadata
        )
    )
    logger.info(f"Created dispatch: {dispatch}")
    logger.info(f"Dialing {phone_number} to room {room_name}")

    # Update CSV with "dialing" status
    await update_csv_status(row_index, "dialing", f"Call initiated at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    sip_participant = await lkapi.sip.create_sip_participant(
        api.CreateSIPParticipantRequest(
            room_name=room_name,
            sip_trunk_id=outbound_trunk_id,
            sip_call_to=phone_number,
            participant_identity="phone_user",
        )
    )
    logger.info(f"Created SIP participant: {sip_participant}")

    await lkapi.aclose()

    # Start monitoring the call status
    asyncio.create_task(monitor_call_status(room_name, row_index, phone_number))

    return True

async def update_csv_status(row_index, status, details=""):
    """Update the status column in the CSV file for a specific row"""
    try:
        # Read current data
        data = []
        with open(csv_file_path, 'r', newline='') as f:
            reader = csv.reader(f)
            headers = next(reader)
            for row in reader:
                data.append(row)

        # Ensure we have enough columns (phone, question, answer, status, details)
        for i, row in enumerate(data):
            while len(row) < 5:
                row.append('')

            # Update the specific row (row_index is 1-based)
            if i + 1 == row_index:
                row[3] = status  # Status column
                row[4] = details  # Details column

        # Write back to CSV
        with open(csv_file_path, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['phone_number', 'question', 'answer', 'status', 'details'])
            writer.writerows(data)

        logger.info(f"Updated CSV row {row_index} with status: {status}")
    except Exception as e:
        logger.error(f"Failed to update CSV status for row {row_index}: {e}")

async def monitor_call_status(room_name, row_index, phone_number):
    """Monitor the call status for a specific room and update CSV accordingly"""
    lkapi = api.LiveKitAPI()

    try:
        # Monitor for up to 3 minutes
        timeout = 180
        start_time = time.time()
        last_status = "dialing"

        while time.time() - start_time < timeout:
            try:
                # Get room participants
                participants = await lkapi.room.list_participants(
                    api.ListParticipantsRequest(room=room_name)
                )

                # Look for the phone participant
                phone_participant = None
                for participant in participants.participants:
                    if participant.identity == "phone_user":
                        phone_participant = participant
                        break

                if phone_participant:
                    # Check SIP call status from attributes
                    call_status = phone_participant.attributes.get('sip.callStatus', 'unknown')

                    if call_status != last_status:
                        logger.info(f"Call status changed for {phone_number}: {last_status} -> {call_status}")

                        if call_status == "ringing":
                            await update_csv_status(row_index, "ringing", f"Phone ringing at {datetime.now().strftime('%H:%M:%S')}")
                        elif call_status == "active":
                            await update_csv_status(row_index, "active", f"Call answered at {datetime.now().strftime('%H:%M:%S')}")
                        elif call_status == "hangup":
                            await update_csv_status(row_index, "completed", f"Call ended at {datetime.now().strftime('%H:%M:%S')}")
                            break

                        last_status = call_status

                # Wait before next check
                await asyncio.sleep(2)

            except Exception as e:
                logger.error(f"Error checking call status for {room_name}: {e}")
                await asyncio.sleep(5)

        # If we timeout without seeing hangup, mark as timeout
        if last_status not in ["hangup", "completed"]:
            await update_csv_status(row_index, "timeout", f"Call monitoring timeout after {timeout}s")

    except Exception as e:
        logger.error(f"Error in call monitoring for {room_name}: {e}")
        await update_csv_status(row_index, "error", f"Monitoring error: {str(e)}")
    finally:
        await lkapi.aclose()

async def read_csv_data():
    """Read the CSV file and return the data"""
    data = []
    with open(csv_file_path, 'r', newline='') as f:
        reader = csv.reader(f)
        headers = next(reader)  # Skip headers
        for i, row in enumerate(reader):
            if len(row) >= 2:
                data.append({
                    'row_index': i + 1,
                    'phone_number': row[0],
                    'question': row[1],
                    'answer': row[2] if len(row) > 2 else '',
                    'status': row[3] if len(row) > 3 else '',
                    'details': row[4] if len(row) > 4 else ''
                })

    return data

async def process_survey_calls():
    """Process all the survey calls in the CSV"""
    # Read the CSV data
    data = await read_csv_data()
    
    logger.info(f"Found {len(data)} survey calls to make")
    
    for item in data:
        if item['answer'] or (item['status'] and item['status'] != ''):
            logger.info(f"Skipping row {item['row_index']} as it already has an answer or status")
            continue
        
        logger.info(f"Processing survey call to {item['phone_number']} with question: {item['question']}")
        
        await make_survey_call(item['phone_number'], item['question'], item['row_index'])

async def main():
    logger.info("Starting survey calls process")
    if not outbound_trunk_id:
        logger.error("SIP_OUTBOUND_TRUNK_ID is not set. Please add it to your .env file.")
        return
    await process_survey_calls()
    logger.info("Survey calls process completed")

if __name__ == "__main__":
    asyncio.run(main())
