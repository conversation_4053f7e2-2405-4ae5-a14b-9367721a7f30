import logging
import pickle

from livekit.agents.llm import ChatContext
from livekit.plugins import openai, rag

annoy_index = rag.annoy.AnnoyIndex.load("src/agents/vdb_data")

embeddings_dimension = 1536
with open("src/agents/agent_data.pkl", "rb") as f:
    paragraphs_by_uuid = pickle.load(f)
_logger = logging.getLogger(__name__)
_logger.propagate = False


class RAGSearch:
    @staticmethod
    async def enrich(chat_ctx: ChatContext, msg) -> str:
        if not msg:
            msg = next(message for message in reversed(chat_ctx.messages) if message.role == "user").content

        user_embedding = await openai.create_embeddings(
            input=[msg.content],
            model="text-embedding-3-small",
            dimensions=embeddings_dimension,
        )
        result = annoy_index.query(user_embedding[0].embedding, n=1)[0]
        paragraph = paragraphs_by_uuid[result.userdata]
        if paragraph:
            _logger.info(f"enriching with RAG: {paragraph}")
            chat_ctx.append(text="Context:\n" + paragraph,
                            role="assistant")
            return paragraph
