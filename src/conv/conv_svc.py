import asyncio
import base64
import logging
from dataclasses import dataclass
from typing import Dict, List, Any, Optional

import aiohttp
import sentry_sdk

from app.base_svcs import get_now
from app.config import get_config
from app.sentry_config import capture_errors
from log.sentry_decorators import sentry_span

_logger = logging.getLogger(__name__)
_logger.propagate = False


@dataclass
class Voice:
    id: Optional[str] = None
    voiceId: Optional[str] = None
    name: Optional[str] = None
    previewUrl: Optional[str] = None
    gender: Optional[str] = None
    accent: Optional[str] = None
    voiceSettings: Optional[str] = None
    createdAt: Optional[str] = None
    updatedAt: Optional[str] = None


@dataclass
class Mission:
    id: Optional[str] = None
    humanName: Optional[str] = None
    intro: Optional[str] = None
    goal: Optional[str] = None
    offerDetails: Optional[str] = None
    farewell: Optional[str] = None
    createdAt: Optional[str] = None
    updatedAt: Optional[str] = None

    def get_prompt(self) -> str:
        intro = f"{self.intro} Your name is {self.humanName}" if self.intro else f"Your name is {self.humanName}"
        goal = ""  # "{human_name} want to peace in the world"
        parts = [intro, goal, self.offerDetails, self.farewell]
        return f"\n .".join(filter(None, parts)).replace("{human_name}", self.humanName)


@dataclass
class AgentInfo:
    id: Optional[str] = None
    name: Optional[str] = None
    model: Optional[str] = None
    voiceId: Optional[str] = None
    companyId: Optional[str] = None
    missionId: Optional[str] = None
    langfusePromptId: Optional[str] = None
    createdAt: Optional[str] = None
    updatedAt: Optional[str] = None
    voice: Optional[Voice] = None
    mission: Optional[Mission] = None


class ConvSvc:
    def __init__(self, conversation_type: str = 'outbound-campaign-call'):
        api_conf = get_config().core_api
        self.base_url = api_conf.url
        auth = base64.b64encode(
            f"{api_conf.login}:{api_conf.password}".encode()
        ).decode()
        self.headers = {
            "Authorization": f"Basic {auth}",
            "Content-Type": "application/json",
            "conversation-type": conversation_type,
        }

    @capture_errors
    @sentry_span(op="conv.update", description="update conversation data")
    async def update_recipient_conversation(
            self, conversation_id: str,
            data: Dict[str, Any]
    ) -> Dict[str, Any]:
        current_span = sentry_sdk.Hub.current.scope.span
        try:
            _logger.debug(f"no dev server")
            # async with aiohttp.ClientSession(headers=self.headers) as session:
            #     url = f"{self.base_url}/v2/system/recipient-conversation/{conversation_id}"
            #     # url = f"{self.base_url}/v1/recipient-conversation/{conversation_id}"
            #     _logger.debug(f"PATCH Request to URL: {url} with Data: {data}")
            #
            #     async with session.patch(url, json=data) as response:
            #         current_span = sentry_sdk.Hub.current.scope.span
            #         if current_span:
            #             current_span.set_data("http.status_code", response.status)
            #
            #         response.raise_for_status()
            #         result = await response.json()
            #
            #         # Optionally sanitize the response before logging
            #         sanitized_result = self._sanitize_response(result)
            #         if current_span:
            #             current_span.set_data("response_body", sanitized_result)
            #
            #         _logger.info(f"PATCH {url} - {data} = {result}")
            #         return result
        except aiohttp.ClientError as http_err:
            _logger.error(f"HTTP error occurred while updating conversation: {http_err}")
            sentry_sdk.capture_exception(http_err)
            if current_span:
                current_span.set_status("error")
            raise
        except Exception as e:
            _logger.exception(f"Unexpected error occurred while updating conversation: {e}")
            sentry_sdk.capture_exception(e)
            if current_span:
                current_span.set_status("error")

    @capture_errors
    @sentry_span(op="conv.sentiment.add", description="add sentiment marks")
    async def add_sentiment(
            self, conversation_id: str,
            sentiment_scores: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        current_span = sentry_sdk.Hub.current.scope.span
        data = {
            "sentimentScores": [
                {
                    "label": score["sentiment"],
                    "score": score["sentimentScore"]
                }
                for score in sentiment_scores
            ],
            "createdAt": get_now(),
        }
        try:
            _logger.debug(f"no dev server")
            # async with aiohttp.ClientSession(headers=self.headers) as session:
            #     # url = f"{self.base_url}/v1/system/recipient-conversation/{conversation_id}/add-sentiment"
            #     url = f"{self.base_url}/v2/system/conversation/{conversation_id}/sentiment"
            #     _logger.debug(f"POST Request to URL: {url} with Data: {data}")
            #
            #     async with session.post(url=url, json=data) as response:
            #         # Access the current span from Sentry
            #         current_span = sentry_sdk.Hub.current.scope.span
            #         if current_span:
            #             current_span.set_data("http.status_code", response.status)
            #
            #         response.raise_for_status()
            #         result = await response.json()
            #
            #         # Optionally sanitize the response before logging
            #         sanitized_result = self._sanitize_response(result)
            #         if current_span:
            #             current_span.set_data("response_body", sanitized_result)
            #
            #         _logger.info(f"POST {url} - {data} = {result}")
            #         return result
        except aiohttp.ClientError as http_err:
            _logger.error(f"HTTP error occurred while adding sentiment: {http_err}")
            sentry_sdk.capture_exception(http_err)
            if current_span:
                current_span.set_status("error")
            # raise
        except Exception as e:
            _logger.exception(f"Unexpected error occurred while adding sentiment: {e}")
            sentry_sdk.capture_exception(e)
            if current_span:
                current_span.set_status("error")

    @capture_errors
    @sentry_span(op="conv.schedule_callback", description="schedule callback")
    async def request_callback(
            self,
            conversation_id: str,
            callback_time: str,
    ) -> Dict[str, Any]:
        data = {
            "interactionResult": "callback_requested",
            "callbackAt": callback_time
        }
        try:
            result = await self.update_recipient_conversation(conversation_id=conversation_id, data=data)
            _logger.info(f"Callback requested for conversation_id: {conversation_id} at {callback_time}")
            return {'success': True}
        except Exception as e:
            _logger.exception(f"Error requesting callback: {e}")
            sentry_sdk.capture_exception(e)
            return None

    @capture_errors
    @sentry_span(op="conv.set_donotcall", description="set do-not-call")
    async def set_do_not_call(self, conversation_id: str) -> None:
        data = {
            "interactionResult": "do_not_call"
        }
        try:
            await self.update_recipient_conversation(conversation_id=conversation_id, data=data)
            _logger.info(f"Set do_not_call for conversation_id: {conversation_id}")
        except Exception as e:
            _logger.exception(f"Error setting do_not_call: {e}")
            sentry_sdk.capture_exception(e)

    @capture_errors
    @sentry_span(op="conv.send_followup", description="request follow-up")
    async def request_follow_up(
            self,
            conversation_id: str,
    ) -> Dict[str, Any]:
        data = {
            "interactionResult": "follow_up_required"
        }
        try:
            result = await self.update_recipient_conversation(conversation_id=conversation_id, data=data)
            _logger.info(f"Follow-up requested for conversation_id: {conversation_id}")
            return {"success": True}
        except Exception as e:
            _logger.exception(f"Error requesting follow-up: {e}")
            sentry_sdk.capture_exception(e)

    @capture_errors
    @sentry_span(op="conv.save_result", description="save conversation result")
    async def save_conversation_result(
            self,
            conversation_id: str,
            result,
            is_final: bool = False
    ):
        data = {
            "result": str(result),
            "isFinal": is_final
        }
        current_span = sentry_sdk.Hub.current.scope.span
        max_retries = 3
        retry_count = 0

        while retry_count <= max_retries:
            try:
                retry_count = max_retries + 1
                _logger.debug(f"no dev server")
                # async with aiohttp.ClientSession(headers=self.headers) as session:
                # url = f"{self.base_url}/v2/system/conversation/{conversation_id}/result"
                # _logger.debug(f"POST Request to URL: {url} with Data: {data}")
                #
                # async with session.post(url, json=data) as response:
                #     if current_span:
                #         current_span.set_data("http.status_code", response.status)
                #
                #     response.raise_for_status()
                #     result = await response.json()
                #
                #     sanitized_result = self._sanitize_response(result)
                #     if current_span:
                #         current_span.set_data("response_body", sanitized_result)
                #
                #     _logger.info(f"POST {url} - {data} = {sanitized_result}")
                return ""

            except aiohttp.ClientError as http_err:
                retry_count += 1
                if retry_count > max_retries:
                    _logger.error(
                        f"HTTP error occurred while saving conversation result after {max_retries} retries: {http_err}")
                    sentry_sdk.capture_exception(http_err)
                    if current_span:
                        current_span.set_status("error")
                    # raise
                else:
                    _logger.warning(
                        f"HTTP error occurred while saving conversation result. Retry attempt {retry_count}/{max_retries}: {http_err}")
                    await asyncio.sleep(1)  # Wait 1 second before retrying
            except Exception as e:
                _logger.exception(f"Unexpected error occurred while saving conversation result: {e}")
                sentry_sdk.capture_exception(e)
                if current_span:
                    current_span.set_status("error")
                # raise
                break  # Don't retry on non-HTTP errors

    def _sanitize_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitizes the response by masking or removing sensitive information.

        Args:
            response (Dict[str, Any]): The original response data.

        Returns:
            Dict[str, Any]: The sanitized response data.
        """
        # Implement sanitization logic as needed
        sensitive_fields = ["auth_token"]
        sanitized = response.copy()
        for field in sensitive_fields:
            if field in sanitized:
                sanitized[field] = "MASKED"
        return sanitized
