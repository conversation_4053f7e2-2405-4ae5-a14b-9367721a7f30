{"functions": ["send_follow_up_message"], "type": "outbound-campaign-call", "analysis": {"prompt": "call_metadata: {client_name: , agent_name: , call_date: , call_duration: } | feedback_responses: {first_impression: , needs_assessment: , information_quality: , overall_interaction: , next_steps_clarity: } | satisfaction_assessment: {overall_satisfaction: , notable_positives: [], areas_for_improvement: [], completed_questions: true/false} | call_quality_metrics: {client_engagement: , conversation_flow: , questions_answered: all/partial}"}, "llm": {"name": "<PERSON>", "model": "gpt-4o", "language": "ukr", "prompt": "A cookie store that sells sweet eclers"}, "voice": {"name": "Camilla", "voiceId": "uYXf8XasLslADfZ2MB4u", "provider": "11labs", "vad_profile": "strict", "similarity_boost": 0.65, "style": 0, "use_speaker_boost": true}, "context": {"companyId": "62dd9340-4aed-4c08-93a4-bbdef08248b8", "companyName": "local_bank", "conversationId": "610c8d27-3517-4901-9703-7caf450c17f4", "campaignId": "02de8981-622f-4a58-a9f2-f68bfc698995", "timezone": "Asia/Dubai", "participant": {"phoneNumber": "+************", "firstName": "<PERSON>", "lastName": "Bee", "email": "<EMAIL>", "preferredLanguage": "ukr", "customerSegment": "premium", "lastContactDate": "2024-03-20", "notes": "Interested in stocks"}, "callSchedule": {"mondayStart": "09:00:00", "mondayEnd": "18:00:00", "tuesdayStart": "09:00:00", "tuesdayEnd": "18:00:00", "wednesdayStart": "09:00:00", "wednesdayEnd": "18:00:00", "thursdayStart": "09:00:00", "thursdayEnd": "18:00:00", "fridayStart": "09:00:00", "fridayEnd": "18:00:00", "saturdayStart": null, "saturdayEnd": null, "sundayStart": null, "sundayEnd": null}}}