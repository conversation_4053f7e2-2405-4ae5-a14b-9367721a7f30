import argparse
import async<PERSON>
import json
import logging
import os
import sys
from contextvars import ContextVar

import sentry_sdk
from livekit.agents import AutoSubscribe, JobContext, WorkerOptions, cli, JobProcess, RoomInputOptions, metrics, \
    MetricsCollectedEvent, JobExecutorType
from livekit.plugins import noise_cancellation
from livekit.rtc import RemoteTrackPublication, RemoteParticipant

from app.config import get_config
from app.sentry_config import initialize_sentry, capture_errors
from log.sentry_decorators import sentry_transaction, sentry_span

conversation_id_var = ContextVar('conversation_id', default="")

usage_collector = metrics.UsageCollector()

# Initialize configuration and log
config = get_config()
logging.basicConfig(
    level=config.app.log_level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # This ensures output to console
        logging.FileHandler('app.log')  # This will also save logs to a file
    ]
)
logger = logging.getLogger(__name__)

initialize_sentry(config)

from conv.conv_mgr import ConvManager

# Global variable to store agent_file
BASE_DIR = os.path.dirname(__file__)
_AGENT_FILE = "amber.json"


async def entrypoint_with_agent(ctx: JobContext) -> None:
    global _AGENT_FILE
    await entrypoint(ctx, _AGENT_FILE)


def prewarm(job_process: JobProcess) -> None:
    pass


async def get_or_wait_for_metadata(ctx: JobContext, conversation_manager: ConvManager, agent_file):
    # Create a future that will be resolved when metadata is updated
    future = asyncio.get_event_loop().create_future()
    if len(ctx.room.metadata) > 0:
        logger.info("Room metadata already available, using it.")
        future.set_result(ctx.room.metadata)
        return future.result()

    @ctx.room.on("room_metadata_changed")
    def room_metadata_changed(old_metadata, metadata):
        logger.debug(f"Room metadata changed: {metadata}")
        if metadata and not future.done():
            future.set_result(metadata)

    try:
        await asyncio.wait_for(future, timeout=10.0)  # 10 second timeout
        logger.info("Received room metadata within timeout.")
        return future.result()
    except asyncio.TimeoutError:
        logger.warning("Timed out waiting for metadata to be updated")
        if len(ctx.room.metadata) > 0:
            logger.info("Using existing room metadata after timeout.")
            return ctx.room.metadata
        else:
            # Load default metadata from specified agent JSON file
            default_metadata_path = os.path.join(BASE_DIR, "meta_test", agent_file)
            logger.info(f"No room metadata available, attempting to load default metadata from {default_metadata_path}")
            try:
                if not os.path.exists(default_metadata_path):
                    logger.error(f"Default metadata file not found: {default_metadata_path}")
                    sentry_sdk.capture_exception(
                        FileNotFoundError(f"Default metadata file not found: {default_metadata_path}")
                    )
                    raise FileNotFoundError(f"Default metadata file not found: {default_metadata_path}")

                with open(default_metadata_path, 'r') as file:
                    default_metadata = json.load(file)
                    logger.info(f"Successfully loaded default metadata from {default_metadata_path}")
                    return json.dumps(default_metadata)  # Convert to JSON string to match ctx.room.metadata format
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON in default metadata file {default_metadata_path}: {str(e)}")
                sentry_sdk.capture_exception(e)
                raise Exception(f"Invalid JSON in default metadata file: {str(e)}")
            except Exception as e:
                logger.error(f"Failed to load default metadata from {default_metadata_path}: {str(e)}")
                sentry_sdk.capture_exception(e)
                raise Exception(f"Failed to load default metadata: {str(e)}")


@sentry_span(op="room", description="wait callee")
async def wait_for_participant(ctx, conversation_manager: ConvManager):
    # Create a future that will be resolved when participant is fully available
    future = asyncio.get_event_loop().create_future()

    participant_obj = await ctx.wait_for_participant()
    # Check if participant already has tracks
    if len(participant_obj.track_publications) > 0:
        conversation_manager.participant_is_joined.set()
        if not future.done():
            future.set_result(participant_obj)
    else:
        # Event handler for track publications
        @ctx.room.on("track_published")
        @sentry_span(op="room.track_published", description="callee added track")
        def track_published(publication: RemoteTrackPublication, participant: RemoteParticipant):
            logger.info(f"{participant.identity} published track: {publication.name}")
            conversation_manager.participant_is_joined.set()
            if not future.done():
                future.set_result(participant_obj)

    @sentry_span(op="participant.attributes_changed", description="callee changed state")
    @ctx.room.on("participant_attributes_changed")
    def participant_attributes_changed(changed_attributes, participant):
        logger.info(f"{participant.identity} attributes changed {changed_attributes}")
        call_status_changed_to = changed_attributes.get('sip.callStatus', '')
        if call_status_changed_to == 'active':
            conversation_manager.participant_is_joined.set()
            logger.info("callStatus: active")
            # Check if we can resolve the future
            if participant_obj and len(participant_obj.track_publications) > 0 and not future.done():
                future.set_result(participant_obj)
        elif call_status_changed_to == 'hangup':
            conversation_manager.set_end_conversation()
            logger.info("callStatus: hangup. ending conversation")
            if not future.done():
                future.set_exception(Exception("Call hungup before participant was fully available"))
        else:
            logger.info(f"callStatus: {call_status_changed_to} (no action taken)")

    try:
        # Wait for the future to be resolved with a timeout
        await asyncio.wait_for(future, timeout=160.0)  # 30 second timeout
        participant = future.result()
        logger.info(
            f"Participant fully available: {participant} conv:{conversation_manager.conv_meta.context.conversationId}")
        return participant
    except asyncio.TimeoutError:
        logger.warning("Timed out waiting for participant to be fully available")
        # Return the participant anyway, even if not fully ready
        if participant_obj:
            logger.info(f"Returning participant despite timeout: {participant_obj}")
            return participant_obj
        raise Exception("No participant available after timeout")


@capture_errors
@sentry_transaction("conversation", "entry point")
async def entrypoint(ctx: JobContext, agent_file: str = "emma.json") -> None:
    scope = sentry_sdk.get_isolation_scope()
    logger.info(f"Room Name: {ctx.room.name}")

    @sentry_span(op="room.shutdown", description="shutdown")
    async def shutdown_callback(obj=None):
        try:
            if conversation_manager is not None:
                if conversation_manager.conversation_ended:
                    logger.info("Conversation already ended. Skipping shutdown.")
                    return
                conversation_manager.set_end_conversation()
                await ctx.room.disconnect()
        except Exception as e:
            logger.error(f"Error shutting down conversation: {str(e)}")
            sentry_sdk.capture_exception(e)

        logger.info(f"Shutdown callback execution: {obj}")

    ctx.add_shutdown_callback(shutdown_callback)

    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)

    try:
        conversation_manager = ConvManager(ctx)
        is_inbound_call = ctx.room.name.startswith("inbound-call")
        logger.info(f"is_inbound_call: {is_inbound_call}")
        metadata = await get_or_wait_for_metadata(ctx, conversation_manager, agent_file)
        conversation_manager.update_metadata(metadata)
    except Exception as e:
        logger.error(f"Failed to initialize ConvManager: {str(e)}")
        logger.error(f"Current room metadata: {ctx.room.metadata!r}")
        raise

    await run_conversation(ctx, conversation_manager, wait_for_participant)


@sentry_span(op="conversation", description="run conversation")
async def run_conversation(ctx: JobContext, conversation_manager: ConvManager, wait_for_participant_fn):
    # Start the conversation
    jana = await conversation_manager.start_conversation()

    participant = await wait_for_participant_fn(ctx, conversation_manager)
    await conversation_manager.session.start(room=ctx.room, agent=jana, room_input_options=RoomInputOptions(
        noise_cancellation=noise_cancellation.BVCTelephony(),
    ))

    @conversation_manager.session.on("metrics_collected")
    def _on_metrics_collected(ev: MetricsCollectedEvent):
        usage_collector.collect(ev.metrics)

    async def log_usage():
        summary = usage_collector.get_summary()
        logger.warning(f"Usage: {summary}")

    ctx.add_shutdown_callback(log_usage)
    await conversation_manager.say_welcome()


def main() -> None:
    # Debug: Log raw sys.argv
    logger.info(f"Raw sys.argv: {sys.argv}")

    # Create a custom parser to handle our arguments
    parser = argparse.ArgumentParser(
        description="Run the application with optional agent configuration",
        add_help=False  # Disable default help to avoid conflicts with click
    )
    subparsers = parser.add_subparsers(dest="command", required=True, help="Command to run (dev or start)")

    # Subparser for 'dev' command
    dev_parser = subparsers.add_parser("dev", help="Run in development mode", add_help=False)
    dev_parser.add_argument(
        "--agent",
        default="emma.json",
        choices=["emma.json", "noah.json", "richard.json", "amber.json"],
        help="Agent configuration JSON file (default: emma.json)"
    )

    # Subparser for 'start' command
    start_parser = subparsers.add_parser("start", help="Run in start mode", add_help=False)
    start_parser.add_argument(
        "--agent",
        default="emma.json",
        choices=["emma.json", "noah.json", "richard.json", "amber.json"],
        help="Agent configuration JSON file (default: emma.json)"
    )

    # Parse known arguments, leaving unknown ones for cli.run_app
    args, unknown_args = parser.parse_known_args()

    # Debug: Log parsed arguments
    logger.info(f"Parsed args: command={args.command}, agent={args.agent}")
    logger.info(f"Unknown args: {unknown_args}")

    # Set the global agent_file
    global _AGENT_FILE
    _AGENT_FILE = args.agent

    # Modify sys.argv to pass only the command and unknown args to cli.run_app
    sys.argv = [sys.argv[0], args.command] + unknown_args

    # Debug: Log modified sys.argv
    logger.info(f"Modified sys.argv for cli.run_app: {sys.argv}")

    worker_options = WorkerOptions(
        entrypoint_fnc=entrypoint_with_agent,
        job_executor_type=JobExecutorType.THREAD,
        prewarm_fnc=prewarm,
        initialize_process_timeout=240,
    )
    try:
        logger.info(f"Starting application with command: {args.command}, agent: {args.agent}")
        cli.run_app(worker_options)
    except asyncio.CancelledError:
        logger.info("Long-running task was cancelled")
    except Exception as e:
        logger.error(f"Error in main application: {str(e)}", exc_info=True)
        sentry_sdk.capture_exception(e)  # Capture unexpected exceptions
    finally:
        logger.info("Shutting down the application.")


# Entry point
if __name__ == "__main__":
    main()
