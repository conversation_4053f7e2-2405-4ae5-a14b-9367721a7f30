import asyncio
import csv
import json
import logging
import os
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime
from dotenv import load_dotenv
from livekit import api

load_dotenv(dotenv_path=Path(__file__).parent.parent / '.env')

logger = logging.getLogger("call-status-monitor")
logger.setLevel(logging.INFO)

class CallStatusMonitor:
    """Monitor and track call status for survey calls"""
    
    def __init__(self, csv_file_path: Path = None):
        self.csv_file_path = csv_file_path or Path(__file__).parent / "survey_data.csv"
        self.room_name_prefix = "survey-call-"
        
    async def get_active_calls(self) -> List[Dict]:
        """Get list of currently active calls with their status"""
        active_calls = []
        lkapi = api.LiveKitAPI()
        
        try:
            # Get all rooms that match our survey call pattern
            rooms = await lkapi.room.list_rooms(api.ListRoomsRequest())
            
            for room in rooms.rooms:
                if room.name.startswith(self.room_name_prefix):
                    # Extract row index from room name
                    try:
                        row_index = int(room.name.replace(self.room_name_prefix, ""))
                    except ValueError:
                        continue
                    
                    # Get participants for this room
                    participants = await lkapi.room.list_participants(
                        api.ListParticipantsRequest(room=room.name)
                    )
                    
                    call_info = {
                        'room_name': room.name,
                        'row_index': row_index,
                        'created_at': room.creation_time,
                        'num_participants': room.num_participants,
                        'participants': []
                    }
                    
                    # Check each participant
                    for participant in participants.participants:
                        participant_info = {
                            'identity': participant.identity,
                            'name': participant.name,
                            'joined_at': participant.joined_at,
                            'attributes': dict(participant.attributes)
                        }
                        
                        # Extract call status if it's a SIP participant
                        if participant.identity == "phone_user":
                            call_status = participant.attributes.get('sip.callStatus', 'unknown')
                            participant_info['call_status'] = call_status
                            call_info['call_status'] = call_status
                        
                        call_info['participants'].append(participant_info)
                    
                    active_calls.append(call_info)
                    
        except Exception as e:
            logger.error(f"Error getting active calls: {e}")
        finally:
            await lkapi.aclose()
            
        return active_calls
    
    async def get_call_status_summary(self) -> Dict:
        """Get a summary of call statuses from CSV and active rooms"""
        summary = {
            'total_calls': 0,
            'dialing': 0,
            'ringing': 0,
            'active': 0,
            'completed': 0,
            'timeout': 0,
            'error': 0,
            'pending': 0,
            'active_rooms': []
        }
        
        # Read CSV data
        try:
            with open(self.csv_file_path, 'r', newline='') as f:
                reader = csv.reader(f)
                headers = next(reader)  # Skip headers
                
                for row in reader:
                    if len(row) >= 2:  # At least phone and question
                        summary['total_calls'] += 1
                        status = row[3] if len(row) > 3 else 'pending'
                        
                        if status in summary:
                            summary[status] += 1
                        elif status == '':
                            summary['pending'] += 1
                        else:
                            # Unknown status, count as pending
                            summary['pending'] += 1
        except FileNotFoundError:
            logger.warning(f"CSV file not found: {self.csv_file_path}")
        except Exception as e:
            logger.error(f"Error reading CSV: {e}")
        
        # Get active calls
        summary['active_rooms'] = await self.get_active_calls()
        
        return summary
    
    async def print_status_report(self):
        """Print a formatted status report"""
        summary = await self.get_call_status_summary()
        
        print("\n" + "="*60)
        print("CALL STATUS REPORT")
        print("="*60)
        print(f"Total Calls: {summary['total_calls']}")
        print(f"Pending:     {summary['pending']}")
        print(f"Dialing:     {summary['dialing']}")
        print(f"Ringing:     {summary['ringing']}")
        print(f"Active:      {summary['active']}")
        print(f"Completed:   {summary['completed']}")
        print(f"Timeout:     {summary['timeout']}")
        print(f"Error:       {summary['error']}")
        print("-"*60)
        
        if summary['active_rooms']:
            print(f"ACTIVE ROOMS ({len(summary['active_rooms'])})")
            print("-"*60)
            for call in summary['active_rooms']:
                status = call.get('call_status', 'unknown')
                print(f"Room: {call['room_name']} | Status: {status} | Participants: {call['num_participants']}")
                
                # Show participant details
                for participant in call['participants']:
                    if participant['identity'] == 'phone_user':
                        attrs = participant.get('attributes', {})
                        sip_status = attrs.get('sip.callStatus', 'unknown')
                        print(f"  └─ Phone User: {sip_status}")
        else:
            print("No active rooms found")
        
        print("="*60)
    
    async def cleanup_completed_rooms(self):
        """Clean up rooms for completed calls"""
        lkapi = api.LiveKitAPI()
        cleaned_count = 0
        
        try:
            active_calls = await self.get_active_calls()
            
            for call in active_calls:
                call_status = call.get('call_status', 'unknown')
                
                # Clean up rooms where call has ended
                if call_status in ['hangup', 'completed'] or call['num_participants'] == 0:
                    try:
                        await lkapi.room.delete_room(
                            api.DeleteRoomRequest(room=call['room_name'])
                        )
                        logger.info(f"Cleaned up room: {call['room_name']}")
                        cleaned_count += 1
                    except Exception as e:
                        logger.error(f"Error cleaning up room {call['room_name']}: {e}")
                        
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
        finally:
            await lkapi.aclose()
            
        return cleaned_count

async def main():
    """Main function for command line usage"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Monitor call status")
    parser.add_argument("--report", action="store_true", help="Show status report")
    parser.add_argument("--cleanup", action="store_true", help="Clean up completed rooms")
    parser.add_argument("--csv", type=str, help="Path to CSV file")
    
    args = parser.parse_args()
    
    csv_path = Path(args.csv) if args.csv else None
    monitor = CallStatusMonitor(csv_path)
    
    if args.report:
        await monitor.print_status_report()
    
    if args.cleanup:
        cleaned = await monitor.cleanup_completed_rooms()
        print(f"Cleaned up {cleaned} completed rooms")
    
    if not args.report and not args.cleanup:
        # Default: show report
        await monitor.print_status_report()

if __name__ == "__main__":
    asyncio.run(main())
