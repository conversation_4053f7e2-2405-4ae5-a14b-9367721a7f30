[[OutboundCallsConsumer]
|
|-- Creates LiveKit Room
|
|-- Adds SIP Participant to Room
|
|-- SIP Provider Dials Callee's Phone
|
|-- Callee's Phone Rings
|
|-- [Event: ParticipantConnected]
| - SIP participant connected to room
| - Not yet an indication that callee answered
|
|-- Callee Answers Call
|
|-- SIP Session Established
|
|-- SIP Participant Starts Publishing Media
|
|-- [Event: TrackPublished]

- Callee has answered and joined the room
- System updates conversation status